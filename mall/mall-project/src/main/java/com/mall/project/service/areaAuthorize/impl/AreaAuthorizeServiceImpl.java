package com.mall.project.service.areaAuthorize.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.areaAuthorize.AreaAuthorizeDao;
import com.mall.project.dto.areaAuthorize.AreaAuthorize;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isEmpty;
import static com.mall.common.util.StringUtils.isNotEmpty;

@Service
@Slf4j
public class AreaAuthorizeServiceImpl implements AreaAuthorizeService {

    @Autowired
    private AreaAuthorizeDao areaAuthorizeDao;

    @Override
    public Map<String, Object> getArea(String areaId) {
        // 判断areaId只能输入整数
        if(!areaId.isEmpty() && !areaId.matches("^-?\\d+$")){
            throw new BusinessException("区域编号只能为整数！");
        }
        List<Map<String, Object>> areaList = areaAuthorizeDao.getArea(areaId);
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("areaList",areaList);
        return dataMap;
    }
    /**
     * 获取合作企业
     */
    @Override
    public Map<String, Object> getCooperateEnterprise() {
        List<Map<String, Object>> cooperateEnterprise = areaAuthorizeDao.getCooperateEnterprise();
        Map<String,Object> dataMap = new HashMap<>();
        // 转换下划线格式为驼峰格式
        cooperateEnterprise = cooperateEnterprise.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        dataMap.put("cooperateEnterprise",cooperateEnterprise);
        return dataMap;
    }


    @Override
    public int saveAreaAuthorize(AreaAuthorize pojo, int updatePerson) {
        if(!pojo.getAreaId().isEmpty() && !pojo.getAreaId().matches("^-?\\d+$")){
            throw new BusinessException("区域编号只能为整数！");
        }
        // 验证手机号格式
        if(isNotEmpty(pojo.getPhone()) && !pojo.getPhone().matches("^1[3-9]\\d{9}$")){
            throw new BusinessException("手机号格式不正确");
        }
        // 验证开始时间格式 yyyy-MM-dd HH:mm:ss
        if(isNotEmpty(pojo.getStartTime()) && !pojo.getStartTime().matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd HH:mm:ss格式");
        }
        // 验证结束时间格式 yyyy-MM-dd HH:mm:ss
        if(isNotEmpty(pojo.getEndTime()) && !pojo.getEndTime().matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd HH:mm:ss格式");
        }
        if(isEmpty(pojo.getType()) || !pojo.getType().matches("^[CB]$")){
            throw new BusinessException("授权类型不能为空，且只能为C或B");
        }
        //验证省、市；县、区；镇、街，开关不能为空，而且只能为0或1
        if(isEmpty(pojo.getOnOff()) || !pojo.getOnOff().matches("^[01]$")){
            throw new BusinessException("省、市；县、区；镇、街，开关不能为空，且只能为0或1");
        }
        return areaAuthorizeDao.saveAreaAuthorize(pojo, updatePerson);
    }

    /**
     * 查询授权级别为1、2、3、4的授权信息
     * type 为C或B
     */
    @Override
    public CommonPage<Map<String, Object>> queryAreaAuthorize(String type,String level, int pageNum, int pageSize) {
        if(isEmpty(type) || !type.matches("^[CB]$")){
            throw new BusinessException("授权类型不能为空，且只能为C或B");
        }
        //判断level不能为空，而且只能为1、2、3、4
        if(level == null || level.isEmpty() || !level.matches("^[1234]$")){
            throw new BusinessException("授权级别不能为空，且只能为1、2、3、4");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = areaAuthorizeDao.queryAreaAuthorize(type,Integer.parseInt(level), pageSize, offset);
        if(dataList == null || dataList.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = areaAuthorizeDao.areaAuthorizeCount(type,Integer.parseInt(level));
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        // 转换下划线格式为驼峰格式
        return new CommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
    }

    /**
     * C授权 计算方式,这里只计算C授权的计算方式,B授权方式计算方式在QuantifyCountDao 类中 updateQuantifyCount() 方法中实现了
     * 计算C的补贴金
     */
    public void updateCSubsidy(){
        String onOff = areaAuthorizeDao.checkAreaAuthorizeSet();
        //当C用户状态为正常时,只有区域授权开启时，才进行补贴金的计算
        if(onOff.equals("0")){
            List<Map<String, Object>> cUserDataList = areaAuthorizeDao.queryCWeightStatus();
            for (Map<String, Object> data : cUserDataList) {
                String areaId = data.get("town_code").toString();
                if(data.get("flag").toString().equals("1") || data.get("flag").toString().equals("2")){
                    // 达标,从area 表的level = 4 往上找代理CB
                    //乡镇、县、市、省 一级一级往上找代理的手机号
                    String phone = findAuthorizedPhoneFromLowToHigh(areaId);
                    if(isNotEmpty(phone)){  //达标用户只流失超出上限的部分
                        //log.info("找到授权手机号: {} 对应区域ID: {}", phone, areaId);
                        BigDecimal weight = new BigDecimal(data.get("Weight").toString());
                        BigDecimal deductionMoneyLimit = new BigDecimal(data.get("deduction_money_limit").toString());
                        BigDecimal subsidy = weight.subtract(deductionMoneyLimit);
                        if(subsidy.compareTo(BigDecimal.ZERO) > 0){
                            areaAuthorizeDao.insertCSubsidyLossRecord(data.get("phone").toString(), phone, subsidy,"达标用户超出上限部分流失");
                        }
                    }else{  //如果到了顶级还没有找到手机号，则将分量全部流失
                        BigDecimal weight = new BigDecimal(data.get("Weight").toString());
                        BigDecimal deductionMoneyLimit = new BigDecimal(data.get("deduction_money_limit").toString());
                        BigDecimal subsidy = weight.subtract(deductionMoneyLimit);
                        if(subsidy.compareTo(BigDecimal.ZERO) > 0){
                            areaAuthorizeDao.insertCSubsidyLossRecord(data.get("phone").toString(), phone, subsidy,"达标用户,但该区域没有CB代理，超出上限部分流失");
                        }
                    }
                }else{   // 未达标,从area 表的level = 1 的往下找代理CB
                    // 获取用户所在区域的完整层级路径（省、市、县、乡镇）
                    String phone = findAuthorizedPhoneFromHightToLow(areaId);
                    // 如果找到了手机号，进行后续处理
                    if(isNotEmpty(phone)) {   // 未达标 所有的分量全部流失
                        BigDecimal weight = new BigDecimal(data.get("Weight").toString());
                        if(weight.compareTo(BigDecimal.ZERO) > 0){
                            areaAuthorizeDao.insertCSubsidyLossRecord(data.get("phone").toString(), phone, weight,"未达标用户全部分量流失");
                        }
                    }
                }
            }
        }
        // 当C用户 状态为异常时,C用户补贴金全部流失
        List<Map<String, Object>> cStatusExceptionList = areaAuthorizeDao.queryCStatusException();
        for (Map<String, Object> data : cStatusExceptionList) {
            BigDecimal weight = new BigDecimal(data.get("Weight").toString());
            if(weight.compareTo(BigDecimal.ZERO) > 0){
                areaAuthorizeDao.insertCSubsidyLossRecord(data.get("phone").toString(), null, weight,"C用户状态异常，分量全部流失");
            }
        }
    }

    /**
     * 从省、市、县、乡镇 一级一级往下找代理的手机号
     */
    public String findAuthorizedPhoneFromHightToLow(String areaId) {
        List<Map<String, Object>> userAreaHierarchy = areaAuthorizeDao.getUserAreaHierarchy(areaId);
        String phone = null;

        // 从level=1开始按层级查找用户自己所在的省、市、县、乡镇的授权手机号
        for(Map<String, Object> areaInfo : userAreaHierarchy) {
            String currentAreaId = areaInfo.get("id").toString();
            phone = areaAuthorizeDao.queryAreaAuthorizePhone(currentAreaId);

            if(isNotEmpty(phone)) {
                //int level = Integer.parseInt(areaInfo.get("level").toString());
                //log.info("未达标用户在level={}区域找到授权手机号: {}", level, phone);
                break; // 找到手机号就停止
            }
        }

        // 如果找到了手机号， 则返回手机号
        if(isNotEmpty(phone)) {
            return phone;
        }else{
            return null;
        }
    }

    /**
     * 乡镇、县、市、省 一级一级往上找代理的手机号
     */
    public String findAuthorizedPhoneFromLowToHigh(String areaId) {
        String phone = areaAuthorizeDao.queryAreaAuthorizePhone(areaId);
        // 如果当前区域没有找到手机号，则向上级查找，直到parent_id = 0
        while(isEmpty(phone) && !areaId.equals("0")){
            String parentId = areaAuthorizeDao.queryParentId(areaId);
            if(parentId == null || parentId.equals("0")){
                break; // 已经到达顶级，停止查找
            }
            areaId = parentId;
            phone = areaAuthorizeDao.queryAreaAuthorizePhone(areaId);
        }

        // 如果找到了手机号， 则返回手机号
        if(isNotEmpty(phone)){  //达标用户只流失超出上限的部分
            return phone;
        }else{  //如果到了顶级还没有找到手机号，则将分量全部流失
            return null;
        }
    }

    /**
     * 删除area_authorize表中授权过期的记录: end_time < NOW()
     */
    @Override
    public void deleteExpiredAuthorize() {
        areaAuthorizeDao.deleteExpiredAuthorize();
    }

    @Override
    public int deleteAreaAuthorize(Integer id) {
        if (id == null || id <= 0) {
            throw new BusinessException("授权记录ID不能为空且必须大于0");
        }
        return areaAuthorizeDao.deleteAreaAuthorize(id);
    }
}
