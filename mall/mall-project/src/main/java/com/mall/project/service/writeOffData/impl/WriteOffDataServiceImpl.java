package com.mall.project.service.writeOffData.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.writeOffData.WriteOffDataDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.writeOffData.WriteOffDataService;
import com.mall.project.util.MallBAuthUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 核销数据服务实现类
 */
@Service
@Slf4j
public class WriteOffDataServiceImpl implements WriteOffDataService {
    @Autowired
    private WriteOffDataDao writeOffDataDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 获取mallB系统的核销数据
     */
    @Override
    public void getWriteOffDataFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取核销数据
            ResponseEntity<String> response = mallBAuthUtils.getForEntity("/mall/receptionA/getUserDeductionStat", String.class);

            // 检查获取核销数据是否成功
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB核销数据失败: " + response.getStatusCode());
            }

            // 解析核销数据响应
            String responseBody = response.getBody();
            if (responseBody == null) {
                throw new BusinessException("mallB核销数据为空");
            }

            // 解析响应JSON
            JsonNode root = objectMapper.readTree(responseBody);

            if (root.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB核销数据失败: " + root.get("msg").asText());
            }

            // 获取mallB核销数据列表
            JsonNode dataList = root.path("data");
            if (!dataList.isArray() || dataList.isEmpty()) {
                log.info("mallB核销数据为空");
                return;
            }

            // 存储核销数据到本地系统
            log.info("开始处理并存储mallB核销数据，共 {} 条记录", dataList.size());

            // 遍历核销数据并存储
            for (JsonNode dataItem : dataList) {
                try {
                    String updateDate = dataItem.path("updateDate").asText();
                    String phone = dataItem.path("phone").asText();
                    String writeOffSubsidy = dataItem.path("writeOffSubsidy").asText();
                    String writeOffSubsidyTotal = dataItem.path("writeOffSubsidyTotal").asText();
                    String unWriteOffSubsidy = dataItem.path("unWriteOffSubsidy").asText();

                    // 调用DAO层存储数据
                    writeOffDataDao.saveWriteOffData(updateDate, phone, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy);

                } catch (Exception e) {
                    log.error("处理mallB核销数据项失败: {}", e.getMessage(), e);
                    // 继续处理下一条记录，不要直接返回
                }
            }
            log.info("成功完成mallB核销数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取mallB系统的促销金数据
     */
    @Override
    public void getPromotionDataFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取促销金数据
            ResponseEntity<String> response = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/getListPromotionDetail", String.class);

            // 检查获取促销金数据是否成功
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB促销金数据失败: " + response.getStatusCode());
            }

            // 解析促销金数据响应
            String responseBody = response.getBody();
            if (responseBody == null) {
                throw new BusinessException("mallB促销金数据为空");
            }

            // 解析响应JSON
            JsonNode root = objectMapper.readTree(responseBody);

            if (root.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB促销金数据失败: " + root.get("msg").asText());
            }

            // 获取mallB促销金数据列表
            JsonNode dataList = root.path("data");
            if (!dataList.isArray() || dataList.isEmpty()) {
                log.info("mallB促销金数据为空");
                return;
            }

            // 存储促销金数据到本地系统
            log.info("开始处理并存储mallB促销金数据，共 {} 条记录", dataList.size());

            // 遍历促销金数据并存储
            for (JsonNode dataItem : dataList) {
                try {

                    String phone = dataItem.path("phone").asText();
                    String promotionUsed = dataItem.path("promotionUsed").asText();
                    String totalPromotionUsed = dataItem.path("totalPromotionUsed").asText();

                    // 调用DAO层存储数据
                    writeOffDataDao.savePromotionData(phone, promotionUsed, totalPromotionUsed);

                } catch (Exception e) {
                    log.error("处理mallB促销金数据项失败: {}", e.getMessage(), e);
                    return;
                }
            }
            log.info("成功完成mallB促销金数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从mallB系统获取核销值数据
     */
    @Override
    public void getWriteOffGoldFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取核销值数据
            ResponseEntity<String> response = mallBAuthUtils.getForEntity("/mall/receptionA/getAdminDailyQuantify", String.class);

            // 检查获取核销值数据是否成功
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB核销值数据失败: " + response.getStatusCode());
            }

            // 解析核销值数据响应
            String responseBody = response.getBody();
            if (responseBody == null) {
                throw new BusinessException("mallB核销值数据为空");
            }

            // 解析响应JSON
            JsonNode root = objectMapper.readTree(responseBody);

            if (root.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB核销值数据失败: " + root.get("msg").asText());
            }

            // 获取mallB核销值数据列表
            JsonNode dataList = root.path("data");
            if (dataList.isEmpty()) {
                log.info("mallB核销值数据为空");
                return;
            }


            // 存储核销值数据到本地系统
            log.info("开始处理并存储mallB核销值数据，共 {} 条记录", dataList.size());

            String phone = dataList.path("phone").asText();
            String writeOffGold = dataList.path("dailyQuantify").asText();
            writeOffDataDao.saveWriteOffGold(phone, writeOffGold);
            log.info("成功完成mallB核销值数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }



    /**
     * 查询核销数据,分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryWriteOffDataPages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero) {
        // 验证开始日期格式 yyyy-MM-dd
        if (isNotEmpty(startDate) && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (isNotEmpty(endDate) && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = writeOffDataDao.queryWriteOffDataPages(phone, startDate, endDate, pageSize, offset, isGreaterThanZero);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = writeOffDataDao.totalWriteOffData(phone, startDate, endDate , isGreaterThanZero);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        WriteOffDataServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new WriteOffDataServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalWriteOffSubsidy", todayTotalWriteOffSubsidy(phone,startDate));   //今日核销补贴金
        summary.put("totalWriteOffSubsidy", totalWriteOffSubsidy(phone,startDate));   //累计已核销补贴金
        summary.put("totalUnWriteOffSubsidy", totalUnWriteOffSubsidy(phone,startDate));   //累计未核销补贴金
        summary.put("todayPromotionUsed", todayPromotionUsed(phone,startDate));   //今日已核销促销金
        summary.put("totalPromotionUsed", totalPromotionUsed(phone,startDate));   //总累计使用金额
        summary.put("totalWriteOffGold", totalWriteOffGold(phone,startDate));   //累计核销值
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 导出核销数据 Excel
     */
    @Override
    public List<Map<String, Object>> exportWriteOffDataExcel(String phone, String startDate, String endDate) {
        List<Map<String, Object>> dataList = writeOffDataDao.exportWriteOffDataExcel(phone, startDate, endDate);
        if(dataList.isEmpty()){
            throw new RuntimeException("暂无数据");
        }
        // 转换下划线格式为驼峰格式
        return dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    /**
     * 统计 今日核销补贴金
     */
    @Override
    public String todayTotalWriteOffSubsidy(String phone,String startDate) {
        return writeOffDataDao.todayTotalWriteOffSubsidy(phone,startDate) == null ? "0" : writeOffDataDao.todayTotalWriteOffSubsidy(phone,startDate);
    }

    /**
     * 统计 累计已核销补贴金
     */
    @Override
    public String totalWriteOffSubsidy(String phone,String startDate) {
        return writeOffDataDao.totalWriteOffSubsidy(phone,startDate) == null ? "0" : writeOffDataDao.totalWriteOffSubsidy(phone,startDate);
    }

    /**
     * 统计 累计未核销补贴金
     */
    @Override
    public String totalUnWriteOffSubsidy(String phone,String startDate) {
        return writeOffDataDao.totalUnWriteOffSubsidy(phone,startDate) == null ? "0" : writeOffDataDao.totalUnWriteOffSubsidy(phone,startDate);
    }

    /**
     * 统计 今日已核销促销金
     */
    @Override
    public String todayPromotionUsed(String phone, String startDate) {
        return writeOffDataDao.todayPromotionUsed(phone,startDate) == null ? "0" : writeOffDataDao.todayPromotionUsed(phone,startDate);
    }
    /**
     * 统计 总累计使用金额
     */
    @Override
    public String totalPromotionUsed(String phone, String startDate) {
        return writeOffDataDao.totalPromotionUsed(phone,startDate) == null ? "0" : writeOffDataDao.totalPromotionUsed(phone,startDate);
    }
    /**
     * 统计 累计核销值
     */
    @Override
    public String totalWriteOffGold(String phone, String startDate) {
        return writeOffDataDao.totalWriteOffGold(phone,startDate) == null ? "0" : writeOffDataDao.totalWriteOffGold(phone,startDate);
    }
}
