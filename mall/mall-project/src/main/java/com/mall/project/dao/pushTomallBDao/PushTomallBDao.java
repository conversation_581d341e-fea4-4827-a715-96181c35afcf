package com.mall.project.dao.pushTomallBDao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 推送数据到mallB系统数据访问对象
 */
@Repository
public class PushTomallBDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 推送量化率到mallB系统
     */
    public Map<String, Object> pushQuantizationRate() {
        try {
            String sql = "SELECT quantify_rate,quantify_date FROM quantization_rate where status = 0 AND DATE(quantify_date) = CURDATE() - INTERVAL 1 DAY";
            return jdbcTemplate.queryForMap(sql);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 更新推送状态
     */
    public void updatePushStatus() {
        String sql = "UPDATE quantization_rate SET status = 1 WHERE DATE(quantify_date) = CURDATE() - INTERVAL 1 DAY";
        jdbcTemplate.update(sql);
    }

    /**
     * 将合作企业各IDB设置 推送至mallB系统
     */
    public Map<String, Object> pushBSettings() {
        String sql = "SELECT quantify_to_credit,credit_to_coupon FROM BC_settings WHERE type = 'B'";
        return jdbcTemplate.queryForMap(sql);
    }

    /**
     * 推送B/C授权到mallB系统
     */
    public List<Map<String, Object>> pushAreaAuthorize() {
        String sql = "SELECT id,phone,area_id,start_time,end_time,type,update_time FROM area_authorize Where DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForList(sql);
    }
}
